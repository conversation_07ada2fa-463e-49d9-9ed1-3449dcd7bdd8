/**
 * 测试 Composition API 版本的登录页面
 */

const fs = require('fs');
const path = require('path');

function testCompositionApiLogin() {
    console.log('🔍 测试 Composition API 版本的登录页面...');
    
    const loginPagePath = path.join(__dirname, 'pages/sys/login/index.vue');
    const backupPath = path.join(__dirname, 'pages/sys/login/index-options-backup.vue');
    
    if (!fs.existsSync(loginPagePath)) {
        console.log('❌ 登录页面文件不存在');
        return;
    }
    
    if (!fs.existsSync(backupPath)) {
        console.log('❌ 备份文件不存在');
        return;
    }
    
    const content = fs.readFileSync(loginPagePath, 'utf8');
    
    // 检查是否使用了 <script setup>
    if (!content.includes('<script setup>')) {
        console.log('❌ 没有使用 <script setup> 语法');
        return;
    }
    console.log('✅ 使用了 <script setup> 语法');
    
    // 检查是否导入了 Composition API
    const requiredImports = [
        'import { ref, reactive, computed, onMounted } from \'vue\'',
        'import { useI18n } from \'vue-i18n\'',
        'import { useStore } from \'vuex\''
    ];
    
    let allImportsFound = true;
    for (const importStatement of requiredImports) {
        if (!content.includes(importStatement)) {
            console.log(`❌ 缺少导入: ${importStatement}`);
            allImportsFound = false;
        }
    }
    
    if (allImportsFound) {
        console.log('✅ 所有必需的 Composition API 导入都存在');
    }
    
    // 检查是否使用了 useI18n
    if (!content.includes('const { t } = useI18n()')) {
        console.log('❌ 没有使用 useI18n()');
        return;
    }
    console.log('✅ 正确使用了 useI18n()');
    
    // 检查是否使用了 useStore
    if (!content.includes('const store = useStore()')) {
        console.log('❌ 没有使用 useStore()');
        return;
    }
    console.log('✅ 正确使用了 useStore()');
    
    // 检查响应式数据定义
    const reactiveDataChecks = [
        'const isDevelopment = ref(',
        'const showClearIcon = ref(',
        'const login = reactive({',
        'const showApiUrlPicker = ref(',
        'const selectedApiUrl = ref(',
        'const customApiUrl = ref(',
        'const phoneNo = ref(',
        'const loginType = ref(',
        'const showPassword = ref(',
        'const remember = ref('
    ];
    
    let allReactiveDataFound = true;
    for (const check of reactiveDataChecks) {
        if (!content.includes(check)) {
            console.log(`❌ 缺少响应式数据定义: ${check}`);
            allReactiveDataFound = false;
        }
    }
    
    if (allReactiveDataFound) {
        console.log('✅ 所有响应式数据定义正确');
    }
    
    // 检查模板中的 t() 调用
    const tCallsInTemplate = content.match(/t\('login\./g);
    if (!tCallsInTemplate || tCallsInTemplate.length < 4) {
        console.log('❌ 模板中的 t() 调用不足');
        return;
    }
    console.log(`✅ 模板中有 ${tCallsInTemplate.length} 个 t() 调用`);
    
    // 检查是否没有 this 引用
    const thisReferences = content.match(/this\./g);
    if (thisReferences) {
        console.log(`⚠️ 仍有 ${thisReferences.length} 个 this 引用需要清理`);
    } else {
        console.log('✅ 没有 this 引用，完全使用 Composition API');
    }
    
    // 检查方法定义
    const methodChecks = [
        'const clearInput = (',
        'const clearIcon = (',
        'const showServerPicker = (',
        'const getApiUrlLabel = (',
        'const onApiUrlConfirm = (',
        'const showPass = (',
        'const submit = ('
    ];
    
    let allMethodsFound = true;
    for (const check of methodChecks) {
        if (!content.includes(check)) {
            console.log(`❌ 缺少方法定义: ${check}`);
            allMethodsFound = false;
        }
    }
    
    if (allMethodsFound) {
        console.log('✅ 所有方法定义正确');
    }
    
    console.log('\n📊 Composition API 转换总结:');
    console.log('• ✅ 使用 <script setup> 语法');
    console.log('• ✅ 导入必需的 Composition API');
    console.log('• ✅ 使用 useI18n() 获取 t 函数');
    console.log('• ✅ 使用 useStore() 获取 store');
    console.log('• ✅ 响应式数据使用 ref/reactive');
    console.log('• ✅ 方法定义使用箭头函数');
    console.log('• ✅ 模板中正确使用 t() 函数');
    
    console.log('\n🎉 Composition API 转换完成！');
    
    console.log('\n📋 优势说明:');
    console.log('1. 直接使用 useI18n() 获取 t 函数，避免全局注入问题');
    console.log('2. 更好的 TypeScript 支持');
    console.log('3. 更清晰的逻辑组织');
    console.log('4. 避免了 this 上下文问题');
    console.log('5. 更好的代码复用性');
    
    console.log('\n🚀 现在可以测试:');
    console.log('• 在 HBuilderX 中运行项目');
    console.log('• 检查登录页面是否正常显示');
    console.log('• 验证 i18n 翻译是否正常工作');
    console.log('• 测试登录功能是否正常');
    console.log('• 检查浏览器控制台是否还有错误');
}

testCompositionApiLogin();
